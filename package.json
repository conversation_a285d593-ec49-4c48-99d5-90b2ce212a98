{"name": "pc-club", "version": "1.0.0", "description": "An Electron application with Vue and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:coverage:ui": "vitest --ui --coverage", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@tencentcloud/chat": "^3.5.7", "electron-updater": "^6.3.9", "events": "^3.3.0", "got": "^14.4.7", "mime-types": "^3.0.1", "systeminformation": "^5.27.7", "ts-md5": "^2.0.1", "uuid": "^11.1.0", "vue": "^3.5.17"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/events": "^3.0.3", "@types/jsdom": "^21.1.7", "@vitejs/plugin-vue": "^6.0.0", "@vitest/ui": "^3.2.4", "c8": "^10.1.3", "electron": "^37.2.3", "electron-builder": "^25.1.8", "electron-vite": "^4.0.0", "eslint": "^9.31.0", "eslint-plugin-vue": "^10.3.0", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^7.0.5", "vitest": "^3.2.4", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^3.0.3"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild"]}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad"}